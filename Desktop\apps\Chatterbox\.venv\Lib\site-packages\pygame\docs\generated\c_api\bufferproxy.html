<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Class BufferProxy API exported by pygame.bufferproxy &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Class Color API exported by pygame.color" href="color.html" />
    <link rel="prev" title="High level API exported by pygame.base" href="base.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="../ref/color.html">Color</a> | 
	    <a href="../ref/display.html">display</a> | 
	    <a href="../ref/draw.html">draw</a> | 
	    <a href="../ref/event.html">event</a> | 
	    <a href="../ref/font.html">font</a> | 
	    <a href="../ref/image.html">image</a> | 
	    <a href="../ref/key.html">key</a> | 
	    <a href="../ref/locals.html">locals</a> | 
	    <a href="../ref/mixer.html">mixer</a> | 
	    <a href="../ref/mouse.html">mouse</a> | 
	    <a href="../ref/rect.html">Rect</a> | 
	    <a href="../ref/surface.html">Surface</a> | 
	    <a href="../ref/time.html">time</a> | 
	    <a href="../ref/music.html">music</a> | 
	    <a href="../ref/pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="../ref/cursors.html">cursors</a> | 
	    <a href="../ref/joystick.html">joystick</a> | 
	    <a href="../ref/mask.html">mask</a> | 
	    <a href="../ref/sprite.html">sprite</a> | 
	    <a href="../ref/transform.html">transform</a> | 
	    <a href="../ref/bufferproxy.html">BufferProxy</a> | 
	    <a href="../ref/freetype.html">freetype</a> | 
	    <a href="../ref/gfxdraw.html">gfxdraw</a> | 
	    <a href="../ref/midi.html">midi</a> | 
	    <a href="../ref/pixelarray.html">PixelArray</a> | 
	    <a href="../ref/pixelcopy.html">pixelcopy</a> | 
	    <a href="../ref/sndarray.html">sndarray</a> | 
	    <a href="../ref/surfarray.html">surfarray</a> | 
	    <a href="../ref/math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="../ref/camera.html">camera</a> | 
	    <a href="../ref/sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="../ref/examples.html">examples</a> | 
	    <a href="../ref/fastevent.html">fastevent</a> | 
	    <a href="../ref/scrap.html">scrap</a> | 
	    <a href="../ref/tests.html">tests</a> | 
	    <a href="../ref/touch.html">touch</a> | 
	    <a href="../ref/pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="class-bufferproxy-api-exported-by-pygame-bufferproxy">
<section id="src-c-bufferproxy-c">
<h2>src_c/bufferproxy.c<a class="headerlink" href="#src-c-bufferproxy-c" title="Link to this heading">¶</a></h2>
<p>This extension module defines Python type <a class="tooltip reference internal" href="../ref/bufferproxy.html#pygame.BufferProxy" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.BufferProxy</span></code><span class="tooltip-content">pygame object to export a surface buffer through an array protocol</span></a>.</p>
<p>Header file: src_c/include/pygame_bufferproxy.h</p>
<dl class="c var">
<dt class="sig sig-object c" id="c.pgBufproxy_Type">
<span class="n"><span class="pre">PyTypeObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgBufproxy_Type</span></span></span><a class="headerlink" href="#c.pgBufproxy_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The pygame buffer proxy object type pygame.BufferProxy.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgBufproxy_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgBufproxy_Check</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgBufproxy_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if Python object <em>x</em> is a <a class="reference internal" href="../ref/bufferproxy.html#pygame.BufferProxy" title="pygame.BufferProxy"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.BufferProxy</span></code></a> instance,
false otherwise.
This will return false on <a class="reference internal" href="../ref/bufferproxy.html#pygame.BufferProxy" title="pygame.BufferProxy"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.BufferProxy</span></code></a> subclass instances as well.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgBufproxy_New">
<span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgBufproxy_New</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span>, <span class="n"><span class="pre">getbufferproc</span></span><span class="w"> </span><span class="n"><span class="pre">get_buffer</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgBufproxy_New" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return a new <a class="reference internal" href="../ref/bufferproxy.html#pygame.BufferProxy" title="pygame.BufferProxy"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.BufferProxy</span></code></a> instance.
Argument <em>obj</em> is the Python object that has its data exposed.
It may be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
Argument <em>get_buffer</em> is the <a class="reference internal" href="base.html#c.pg_buffer" title="pg_buffer"><code class="xref c c-type docutils literal notranslate"><span class="pre">pg_buffer</span></code></a> get callback.
It must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
On failure raise a Python error and return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgBufproxy_GetParent">
<span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgBufproxy_GetParent</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgBufproxy_GetParent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the Python object wrapped by buffer proxy <em>obj</em>.
Argument <em>obj</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
On failure, raise a Python error and return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgBufproxy_Trip">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgBufproxy_Trip</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgBufproxy_Trip" title="Link to this definition">¶</a><br /></dt>
<dd><p>Cause the buffer proxy object <em>obj</em> to create a <a class="reference internal" href="base.html#c.pg_buffer" title="pg_buffer"><code class="xref c c-type docutils literal notranslate"><span class="pre">pg_buffer</span></code></a> view of its parent.
Argument <em>obj</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
Return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, otherwise raise a Python error and return <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p>
</dd></dl>

</section>
</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/c_api\bufferproxy.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="color.html" title="Class Color API exported by pygame.color"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="base.html" title="High level API exported by pygame.base"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../c_api.html" accesskey="U">pygame C API</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Class BufferProxy API exported by pygame.bufferproxy</a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>