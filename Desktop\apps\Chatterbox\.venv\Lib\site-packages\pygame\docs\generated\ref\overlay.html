<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.Overlay &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.PixelArray" href="pixelarray.html" />
    <link rel="prev" title="pygame.mixer.music" href="music.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="pygame-overlay">
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This module is non functional in pygame 2.0 and above, unless you have manually compiled pygame with SDL1.
This module will not be supported in the future.</p>
</div>
<dl class="py class definition">
<dt class="sig sig-object py title" id="pygame.Overlay">
<span class="sig-prename descclassname"><span class="pre">pygame.</span></span><span class="sig-name descname"><span class="pre">Overlay</span></span><a class="headerlink" href="#pygame.Overlay" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">pygame object for video overlay graphics</span></div>
<div class="line"><span class="signature">Overlay(format, (width, height)) -&gt; Overlay</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="overlay.html#pygame.Overlay.display">pygame.Overlay.display</a></div>
</td>
<td>—</td>
<td>set the overlay pixel data</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="overlay.html#pygame.Overlay.set_location">pygame.Overlay.set_location</a></div>
</td>
<td>—</td>
<td>control where the overlay is displayed</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="overlay.html#pygame.Overlay.get_hardware">pygame.Overlay.get_hardware</a></div>
</td>
<td>—</td>
<td>test if the Overlay is hardware accelerated</td>
</tr>
</tbody>
</table>
<p>The Overlay objects provide support for accessing hardware video overlays.
Video overlays do not use standard <code class="docutils literal notranslate"><span class="pre">RGB</span></code> pixel formats, and can use
multiple resolutions of data to create a single image.</p>
<p>The Overlay objects represent lower level access to the display hardware. To
use the object you must understand the technical details of video overlays.</p>
<p>The Overlay format determines the type of pixel data used. Not all hardware
will support all types of overlay formats. Here is a list of available
format types:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">YV12_OVERLAY</span><span class="p">,</span> <span class="n">IYUV_OVERLAY</span><span class="p">,</span> <span class="n">YUY2_OVERLAY</span><span class="p">,</span> <span class="n">UYVY_OVERLAY</span><span class="p">,</span> <span class="n">YVYU_OVERLAY</span>
</pre></div>
</div>
<p>The width and height arguments control the size for the overlay image data.
The overlay image can be displayed at any size, not just the resolution of
the overlay.</p>
<p>The overlay objects are always visible, and always show above the regular
display contents.</p>
<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Overlay.display">
<span class="sig-name descname"><span class="pre">display</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Overlay.display" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">set the overlay pixel data</span></div>
<div class="line"><span class="signature">display((y, u, v)) -&gt; None</span></div>
<div class="line"><span class="signature">display() -&gt; None</span></div>
</div>
<p>Display the YUV data in SDL's overlay planes. The y, u, and v arguments
are strings of binary data. The data must be in the correct format used
to create the Overlay.</p>
<p>If no argument is passed in, the Overlay will simply be redrawn with the
current data. This can be useful when the Overlay is not really hardware
accelerated.</p>
<p>The strings are not validated, and improperly sized strings could crash
the program.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Overlay.set_location">
<span class="sig-name descname"><span class="pre">set_location</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Overlay.set_location" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">control where the overlay is displayed</span></div>
<div class="line"><span class="signature">set_location(rect) -&gt; None</span></div>
</div>
<p>Set the location for the overlay. The overlay will always be shown
relative to the main display Surface. This does not actually redraw the
overlay, it will be updated on the next call to <code class="docutils literal notranslate"><span class="pre">Overlay.display()</span></code>.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Overlay.get_hardware">
<span class="sig-name descname"><span class="pre">get_hardware</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Overlay.get_hardware" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">test if the Overlay is hardware accelerated</span></div>
<div class="line"><span class="signature">get_hardware(rect) -&gt; int</span></div>
</div>
<p>Returns a True value when the Overlay is hardware accelerated. If the
platform does not support acceleration, software rendering is used.</p>
</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref\overlay.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pixelarray.html" title="pygame.PixelArray"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="music.html" title="pygame.mixer.music"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.Overlay</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>