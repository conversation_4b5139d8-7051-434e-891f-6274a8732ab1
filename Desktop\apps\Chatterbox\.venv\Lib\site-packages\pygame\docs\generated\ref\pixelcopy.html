<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.pixelcopy &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame" href="pygame.html" />
    <link rel="prev" title="pygame.PixelArray" href="pixelarray.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame.pixelcopy">
<span id="pygame-pixelcopy"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame.pixelcopy</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">pygame module for general pixel array copying</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="pixelcopy.html#pygame.pixelcopy.surface_to_array">pygame.pixelcopy.surface_to_array</a></div>
</td>
<td>—</td>
<td>copy surface pixels to an array object</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="pixelcopy.html#pygame.pixelcopy.array_to_surface">pygame.pixelcopy.array_to_surface</a></div>
</td>
<td>—</td>
<td>copy an array object to a surface</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="pixelcopy.html#pygame.pixelcopy.map_array">pygame.pixelcopy.map_array</a></div>
</td>
<td>—</td>
<td>copy an array to another array, using surface format</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="pixelcopy.html#pygame.pixelcopy.make_surface">pygame.pixelcopy.make_surface</a></div>
</td>
<td>—</td>
<td>Copy an array to a new surface</td>
</tr>
</tbody>
</table>
<p>The <code class="docutils literal notranslate"><span class="pre">pygame.pixelcopy</span></code> module contains functions for copying between
surfaces and objects exporting an array structure interface. It is a backend
for <a class="tooltip reference internal" href="surfarray.html#module-pygame.surfarray" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.surfarray</span></code><span class="tooltip-content">pygame module for accessing surface pixel data using array interfaces</span></a>, adding NumPy support. But pixelcopy is more
general, and intended for direct use.</p>
<p>The array struct interface exposes an array's data in a standard way.
It was introduced in NumPy. In Python 2.7 and above it is replaced by the
new buffer protocol, though the buffer protocol is still a work in progress.
The array struct interface, on the other hand, is stable and works with earlier
Python versions. So for now the array struct interface is the predominate way
pygame handles array introspection.</p>
<p>For 2d arrays of integer pixel values, the values are mapped to the
pixel format of the related surface. To get the actual color of a pixel
value use <a class="tooltip reference internal" href="surface.html#pygame.Surface.unmap_rgb" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.Surface.unmap_rgb()</span></code><span class="tooltip-content">convert a mapped integer color value into a Color</span></a>. 2d arrays can only be used
directly between surfaces having the same pixel layout.</p>
<p>New in pygame 1.9.2.</p>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.pixelcopy.surface_to_array">
<span class="sig-prename descclassname"><span class="pre">pygame.pixelcopy.</span></span><span class="sig-name descname"><span class="pre">surface_to_array</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.pixelcopy.surface_to_array" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">copy surface pixels to an array object</span></div>
<div class="line"><span class="signature">surface_to_array(array, surface, kind='P', opaque=255, clear=0) -&gt; None</span></div>
</div>
<p>The surface_to_array function copies pixels from a Surface object
to a 2D or 3D array. Depending on argument <code class="docutils literal notranslate"><span class="pre">kind</span></code> and the target array
dimension, a copy may be raw pixel value, RGB, a color component slice,
or colorkey alpha transparency value. Recognized <code class="docutils literal notranslate"><span class="pre">kind</span></code> values are the
single character codes 'P', 'R', 'G', 'B', 'A', and 'C'. Kind codes are case
insensitive, so 'p' is equivalent to 'P'. The first two dimensions
of the target must be the surface size (w, h).</p>
<p>The default 'P' kind code does a direct raw integer pixel (mapped) value
copy to a 2D array and a 'RGB' pixel component (unmapped) copy to a 3D array
having shape (w, h, 3). For an 8 bit colormap surface this means the
table index is copied to a 2D array, not the table value itself. A 2D
array's item size must be at least as large as the surface's pixel
byte size. The item size of a 3D array must be at least one byte.</p>
<p>For the 'R', 'G', 'B', and 'A' copy kinds a single color component
of the unmapped surface pixels are copied to the target 2D array.
For kind 'A' and surfaces with source alpha (the surface was created with
the SRCALPHA flag), has a colorkey
(set with <a class="reference internal" href="surface.html#pygame.Surface.set_colorkey" title="pygame.Surface.set_colorkey"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Surface.set_colorkey()</span></code></a>),
or has a blanket alpha
(set with <a class="reference internal" href="surface.html#pygame.Surface.set_alpha" title="pygame.Surface.set_alpha"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Surface.set_alpha()</span></code></a>)
then the alpha values are those expected for a SDL surface.
If a surface has no explicit alpha value, then the target array
is filled with the value of the optional <code class="docutils literal notranslate"><span class="pre">opaque</span></code> surface_to_array
argument (default 255: not transparent).</p>
<p>Copy kind 'C' is a special case for alpha copy of a source surface
with colorkey. Unlike the 'A' color component copy, the <code class="docutils literal notranslate"><span class="pre">clear</span></code>
argument value is used for colorkey matches, <code class="docutils literal notranslate"><span class="pre">opaque</span></code> otherwise.
By default, a match has alpha 0 (totally transparent), while everything
else is alpha 255 (totally opaque). It is a more general implementation
of <a class="tooltip reference internal" href="surfarray.html#pygame.surfarray.array_colorkey" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.surfarray.array_colorkey()</span></code><span class="tooltip-content">Copy the colorkey values into a 2d array</span></a>.</p>
<p>Specific to surface_to_array, a ValueError is raised for target arrays
with incorrect shape or item size. A TypeError is raised for an incorrect
kind code. Surface specific problems, such as locking, raise a pygame.error.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.pixelcopy.array_to_surface">
<span class="sig-prename descclassname"><span class="pre">pygame.pixelcopy.</span></span><span class="sig-name descname"><span class="pre">array_to_surface</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.pixelcopy.array_to_surface" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">copy an array object to a surface</span></div>
<div class="line"><span class="signature">array_to_surface(&lt;surface&gt;, &lt;array&gt;) -&gt; None</span></div>
</div>
<p>See <a class="tooltip reference internal" href="surfarray.html#pygame.surfarray.blit_array" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.surfarray.blit_array()</span></code><span class="tooltip-content">Blit directly from a array values</span></a>.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.pixelcopy.map_array">
<span class="sig-prename descclassname"><span class="pre">pygame.pixelcopy.</span></span><span class="sig-name descname"><span class="pre">map_array</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.pixelcopy.map_array" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">copy an array to another array, using surface format</span></div>
<div class="line"><span class="signature">map_array(&lt;array&gt;, &lt;array&gt;, &lt;surface&gt;) -&gt; None</span></div>
</div>
<p>Map an array of color element values - (w, h, ..., 3) - to an array of
pixels - (w, h) according to the format of &lt;surface&gt;.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.pixelcopy.make_surface">
<span class="sig-prename descclassname"><span class="pre">pygame.pixelcopy.</span></span><span class="sig-name descname"><span class="pre">make_surface</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.pixelcopy.make_surface" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Copy an array to a new surface</span></div>
<div class="line"><span class="signature">pygame.pixelcopy.make_surface(array) -&gt; Surface</span></div>
</div>
<p>Create a new Surface that best resembles the data and format of the array.
The array can be 2D or 3D with any sized integer values.</p>
</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref\pixelcopy.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pygame.html" title="pygame"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="pixelarray.html" title="pygame.PixelArray"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.pixelcopy</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>