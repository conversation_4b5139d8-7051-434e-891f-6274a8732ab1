#!/usr/bin/env python3
"""
ChatterboxTTS Desktop Application
A comprehensive GUI for text-to-speech generation with voice cloning
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import torch
import torchaudio as ta
import numpy as np
import os
import re
import threading
from datetime import datetime
import tempfile
import pygame
from pathlib import Path
import json

# Try to import required libraries
try:
    from chatterbox.tts import ChatterboxTTS
    CHATTERBOX_AVAILABLE = True
except ImportError:
    CHATTERBOX_AVAILABLE = False
    print("Warning: chatterbox-tts not installed. Install with 'pip install chatterbox-tts'")

try:
    import nltk
    try:
        nltk.data.find('tokenizers/punkt')
        NLTK_AVAILABLE = True
    except LookupError:
        print("Downloading NLTK punkt tokenizer...")
        try:
            nltk.download('punkt_tab')
        except:
            nltk.download('punkt')
        NLTK_AVAILABLE = True
        from nltk.tokenize import sent_tokenize
except ImportError:
    NLTK_AVAILABLE = False
    print("Warning: NLTK not available. Using basic sentence splitting.")

class ChatterboxTTSApp:
    def __init__(self, root):
        self.root = root
        self.root.title("ChatterboxTTS Desktop Application")
        self.root.geometry("1000x800")
        self.root.minsize(900, 700)
        
        # Initialize pygame for audio playback
        pygame.mixer.init()
        
        # Application state
        self.model = None
        self.device = None
        self.generated_sentences = []
        self.audio_files = []
        self.voice_clone_path = None
        self.save_location = os.path.expanduser("~/Desktop")
        self.is_generating = False
        
        # Create GUI
        self.create_widgets()
        self.load_settings()
        
        # Check dependencies
        self.check_dependencies()
    
    def create_widgets(self):
        """Create the main GUI interface"""
        # Main frame with padding
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🎙️ ChatterboxTTS Desktop", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Model loading section
        model_frame = ttk.LabelFrame(main_frame, text="Model Management", padding="10")
        model_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        model_frame.columnconfigure(1, weight=1)
        
        self.load_model_btn = ttk.Button(model_frame, text="🔄 Load Model", 
                                        command=self.load_model_thread)
        self.load_model_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.model_status = ttk.Label(model_frame, text="Model not loaded", 
                                     foreground="red")
        self.model_status.grid(row=0, column=1, sticky=tk.W)
        
        self.device_var = tk.StringVar(value="auto")
        device_frame = ttk.Frame(model_frame)
        device_frame.grid(row=0, column=2)
        ttk.Label(device_frame, text="Device:").pack(side=tk.LEFT)
        device_combo = ttk.Combobox(device_frame, textvariable=self.device_var, 
                                   values=["auto", "cuda", "cpu"], width=8, state="readonly")
        device_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # Text input section
        text_frame = ttk.LabelFrame(main_frame, text="Text Input", padding="10")
        text_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Text editing toolbar
        text_toolbar = ttk.Frame(text_frame)
        text_toolbar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.copy_btn = ttk.Button(text_toolbar, text="📋 Copy", command=self.copy_text, width=8)
        self.copy_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.paste_btn = ttk.Button(text_toolbar, text="📄 Paste", command=self.paste_text, width=8)
        self.paste_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.clear_text_btn = ttk.Button(text_toolbar, text="🗑️ Clear", command=self.clear_text, width=8)
        self.clear_text_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.select_all_btn = ttk.Button(text_toolbar, text="📝 Select All", command=self.select_all_text, width=10)
        self.select_all_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.load_file_btn = ttk.Button(text_toolbar, text="📂 Load File", command=self.load_text_file, width=10)
        self.load_file_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.save_file_btn = ttk.Button(text_toolbar, text="💾 Save File", command=self.save_text_file, width=10)
        self.save_file_btn.pack(side=tk.LEFT)

        # Text input area
        self.text_input = scrolledtext.ScrolledText(text_frame, height=12, wrap=tk.WORD,
                                                   font=('Consolas', 11), undo=True, maxundo=20)
        self.text_input.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Default text
        default_text = ("Welcome to ChatterboxTTS! This is a powerful text-to-speech application. "
                       "You can clone voices, generate speech sentence by sentence, and combine multiple "
                       "audio files into one. Try typing your own text here and click generate!")
        self.text_input.insert('1.0', default_text)

        # Enable right-click context menu
        self.create_text_context_menu()

        # Bind keyboard shortcuts
        self.text_input.bind('<Control-a>', lambda e: self.select_all_text())
        self.text_input.bind('<Control-c>', lambda e: self.copy_text())
        self.text_input.bind('<Control-v>', lambda e: self.paste_text())
        self.text_input.bind('<Control-x>', lambda e: self.cut_text())
        self.text_input.bind('<Control-z>', lambda e: self.undo_text())
        self.text_input.bind('<Control-y>', lambda e: self.redo_text())

        # Give focus to text input
        self.text_input.focus_set()
        
        # Voice cloning section
        voice_frame = ttk.LabelFrame(main_frame, text="Voice Cloning", padding="10")
        voice_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 10))
        voice_frame.columnconfigure(1, weight=1)

        self.load_voice_btn = ttk.Button(voice_frame, text="📁 Load Voice Sample",
                                        command=self.load_voice_sample)
        self.load_voice_btn.grid(row=0, column=0, padx=(0, 10))

        self.voice_path_var = tk.StringVar(value="No voice sample loaded")
        self.voice_path_label = ttk.Label(voice_frame, textvariable=self.voice_path_var)
        self.voice_path_label.grid(row=0, column=1, sticky=tk.W)

        self.clear_voice_btn = ttk.Button(voice_frame, text="❌ Clear",
                                         command=self.clear_voice_sample)
        self.clear_voice_btn.grid(row=0, column=2)

        # Generation options
        options_frame = ttk.LabelFrame(main_frame, text="Generation Options", padding="10")
        options_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Sentence mode
        self.sentence_mode_var = tk.BooleanVar(value=True)
        sentence_check = ttk.Checkbutton(options_frame, text="Generate sentence by sentence", 
                                        variable=self.sentence_mode_var)
        sentence_check.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        
        # Auto-combine
        self.auto_combine_var = tk.BooleanVar(value=True)
        combine_check = ttk.Checkbutton(options_frame, text="Auto-combine sentences", 
                                       variable=self.auto_combine_var)
        combine_check.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        # Auto-play
        self.auto_play_var = tk.BooleanVar(value=False)
        play_check = ttk.Checkbutton(options_frame, text="Auto-play generated audio",
                                    variable=self.auto_play_var)
        play_check.grid(row=0, column=2, sticky=tk.W)

        # Advanced parameters section
        advanced_frame = ttk.LabelFrame(main_frame, text="Advanced Parameters", padding="10")
        advanced_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        advanced_frame.columnconfigure(1, weight=1)
        advanced_frame.columnconfigure(3, weight=1)

        # Add help text
        help_text = ("💡 Exaggeration: Controls speech expressiveness (0.0=neutral, 1.0=very expressive)\n"
                    "💡 CFG/Pace: Controls generation guidance and pacing (lower=faster, higher=slower)\n"
                    "💡 Temperature: Controls randomness (lower=more consistent, higher=more varied)")
        help_label = ttk.Label(advanced_frame, text=help_text, font=('Arial', 8), foreground='gray')
        help_label.grid(row=2, column=0, columnspan=6, sticky=tk.W, pady=(10, 0))

        # Exaggeration parameter
        ttk.Label(advanced_frame, text="Exaggeration:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.exaggeration_var = tk.DoubleVar(value=0.5)
        exaggeration_scale = ttk.Scale(advanced_frame, from_=0.0, to=1.0,
                                     variable=self.exaggeration_var, orient=tk.HORIZONTAL, length=150)
        exaggeration_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.exaggeration_label = ttk.Label(advanced_frame, text="0.50")
        self.exaggeration_label.grid(row=0, column=2, sticky=tk.W, padx=(0, 20))

        # CFG Weight (Pace) parameter
        ttk.Label(advanced_frame, text="CFG/Pace:").grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        self.cfg_weight_var = tk.DoubleVar(value=0.5)
        cfg_weight_scale = ttk.Scale(advanced_frame, from_=0.0, to=1.0,
                                   variable=self.cfg_weight_var, orient=tk.HORIZONTAL, length=150)
        cfg_weight_scale.grid(row=0, column=4, sticky=(tk.W, tk.E), padx=(0, 10))
        self.cfg_weight_label = ttk.Label(advanced_frame, text="0.50")
        self.cfg_weight_label.grid(row=0, column=5, sticky=tk.W)

        # Temperature parameter
        ttk.Label(advanced_frame, text="Temperature:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.temperature_var = tk.DoubleVar(value=0.8)
        temperature_scale = ttk.Scale(advanced_frame, from_=0.1, to=2.0,
                                    variable=self.temperature_var, orient=tk.HORIZONTAL, length=150)
        temperature_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        self.temperature_label = ttk.Label(advanced_frame, text="0.80")
        self.temperature_label.grid(row=1, column=2, sticky=tk.W, padx=(0, 20), pady=(10, 0))

        # Random seed parameter
        ttk.Label(advanced_frame, text="Random Seed:").grid(row=1, column=3, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.random_seed_var = tk.StringVar(value="0")
        seed_entry = ttk.Entry(advanced_frame, textvariable=self.random_seed_var, width=10)
        seed_entry.grid(row=1, column=4, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        ttk.Label(advanced_frame, text="(0 = random)").grid(row=1, column=5, sticky=tk.W, pady=(10, 0))

        # Bind scale events to update labels
        exaggeration_scale.bind("<Motion>", lambda e: self.update_exaggeration_label())
        exaggeration_scale.bind("<ButtonRelease-1>", lambda e: self.update_exaggeration_label())
        cfg_weight_scale.bind("<Motion>", lambda e: self.update_cfg_weight_label())
        cfg_weight_scale.bind("<ButtonRelease-1>", lambda e: self.update_cfg_weight_label())
        temperature_scale.bind("<Motion>", lambda e: self.update_temperature_label())
        temperature_scale.bind("<ButtonRelease-1>", lambda e: self.update_temperature_label())
        
        # Save location section
        save_frame = ttk.LabelFrame(main_frame, text="Save Location", padding="10")
        save_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 10))
        save_frame.columnconfigure(1, weight=1)

        self.browse_save_btn = ttk.Button(save_frame, text="📂 Browse Save Location",
                                         command=self.browse_save_location)
        self.browse_save_btn.grid(row=0, column=0, padx=(0, 10))

        self.save_path_var = tk.StringVar(value=self.save_location)
        self.save_path_label = ttk.Label(save_frame, textvariable=self.save_path_var)
        self.save_path_label.grid(row=0, column=1, sticky=tk.W)

        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=7, column=0, columnspan=3, pady=(10, 0))
        
        self.generate_btn = ttk.Button(control_frame, text="🎵 Generate Audio", 
                                      command=self.generate_audio_thread, style="Accent.TButton")
        self.generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.combine_btn = ttk.Button(control_frame, text="🔗 Combine All", 
                                     command=self.combine_all_audio)
        self.combine_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.play_btn = ttk.Button(control_frame, text="▶️ Play Last", 
                                  command=self.play_last_audio)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="⏹️ Stop", 
                                  command=self.stop_audio)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_btn = ttk.Button(control_frame, text="🗑️ Clear All", 
                                   command=self.clear_all)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.open_folder_btn = ttk.Button(control_frame, text="📁 Open Folder", 
                                         command=self.open_save_folder)
        self.open_folder_btn.pack(side=tk.LEFT)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Status/log area
        log_frame = ttk.LabelFrame(main_frame, text="Status Log", padding="5")
        log_frame.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(9, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=6, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Style configuration
        style = ttk.Style()
        try:
            style.theme_use('clam')
        except:
            pass
    
    def log_message(self, message):
        """Add message to log area"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_exaggeration_label(self):
        """Update exaggeration parameter label"""
        value = self.exaggeration_var.get()
        self.exaggeration_label.config(text=f"{value:.2f}")

    def update_cfg_weight_label(self):
        """Update CFG weight parameter label"""
        value = self.cfg_weight_var.get()
        self.cfg_weight_label.config(text=f"{value:.2f}")

    def update_temperature_label(self):
        """Update temperature parameter label"""
        value = self.temperature_var.get()
        self.temperature_label.config(text=f"{value:.2f}")

    def create_text_context_menu(self):
        """Create right-click context menu for text input"""
        self.text_context_menu = tk.Menu(self.root, tearoff=0)
        self.text_context_menu.add_command(label="Copy", command=self.copy_text, accelerator="Ctrl+C")
        self.text_context_menu.add_command(label="Paste", command=self.paste_text, accelerator="Ctrl+V")
        self.text_context_menu.add_command(label="Cut", command=self.cut_text, accelerator="Ctrl+X")
        self.text_context_menu.add_separator()
        self.text_context_menu.add_command(label="Select All", command=self.select_all_text, accelerator="Ctrl+A")
        self.text_context_menu.add_separator()
        self.text_context_menu.add_command(label="Undo", command=self.undo_text, accelerator="Ctrl+Z")
        self.text_context_menu.add_command(label="Redo", command=self.redo_text, accelerator="Ctrl+Y")

        # Bind right-click to show context menu
        self.text_input.bind("<Button-3>", self.show_text_context_menu)

    def show_text_context_menu(self, event):
        """Show context menu at cursor position"""
        try:
            self.text_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.text_context_menu.grab_release()

    def copy_text(self):
        """Copy selected text to clipboard"""
        try:
            selected_text = self.text_input.selection_get()
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
            self.log_message("📋 Text copied to clipboard")
        except tk.TclError:
            # No text selected, copy all text
            all_text = self.text_input.get('1.0', tk.END).strip()
            if all_text:
                self.root.clipboard_clear()
                self.root.clipboard_append(all_text)
                self.log_message("📋 All text copied to clipboard")

    def paste_text(self):
        """Paste text from clipboard"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text:
                # Insert at cursor position
                self.text_input.insert(tk.INSERT, clipboard_text)
                self.log_message("📄 Text pasted from clipboard")
        except tk.TclError:
            self.log_message("⚠️ No text in clipboard")

    def cut_text(self):
        """Cut selected text to clipboard"""
        try:
            selected_text = self.text_input.selection_get()
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
            self.text_input.delete(tk.SEL_FIRST, tk.SEL_LAST)
            self.log_message("✂️ Text cut to clipboard")
        except tk.TclError:
            self.log_message("⚠️ No text selected to cut")

    def clear_text(self):
        """Clear all text"""
        self.text_input.delete('1.0', tk.END)
        self.log_message("🗑️ Text cleared")

    def select_all_text(self):
        """Select all text"""
        self.text_input.tag_add(tk.SEL, '1.0', tk.END)
        self.text_input.mark_set(tk.INSERT, '1.0')
        self.text_input.see(tk.INSERT)
        return 'break'  # Prevent default behavior

    def undo_text(self):
        """Undo last text operation"""
        try:
            self.text_input.edit_undo()
            self.log_message("↶ Undo")
        except tk.TclError:
            self.log_message("⚠️ Nothing to undo")
        return 'break'

    def redo_text(self):
        """Redo last undone text operation"""
        try:
            self.text_input.edit_redo()
            self.log_message("↷ Redo")
        except tk.TclError:
            self.log_message("⚠️ Nothing to redo")
        return 'break'

    def load_text_file(self):
        """Load text from file"""
        file_path = filedialog.askopenfilename(
            title="Load Text File",
            filetypes=[
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.text_input.delete('1.0', tk.END)
                self.text_input.insert('1.0', content)
                filename = os.path.basename(file_path)
                self.log_message(f"📂 Loaded text from: {filename}")
            except Exception as e:
                self.log_message(f"❌ Error loading file: {str(e)}")
                messagebox.showerror("Error", f"Failed to load file: {str(e)}")

    def save_text_file(self):
        """Save text to file"""
        text_content = self.text_input.get('1.0', tk.END).strip()
        if not text_content:
            messagebox.showwarning("Warning", "No text to save!")
            return

        file_path = filedialog.asksaveasfilename(
            title="Save Text File",
            defaultextension=".txt",
            filetypes=[
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                filename = os.path.basename(file_path)
                self.log_message(f"💾 Text saved to: {filename}")
            except Exception as e:
                self.log_message(f"❌ Error saving file: {str(e)}")
                messagebox.showerror("Error", f"Failed to save file: {str(e)}")
    
    def check_dependencies(self):
        """Check if required dependencies are available"""
        if not CHATTERBOX_AVAILABLE:
            self.log_message("❌ ChatterboxTTS not installed. Please install with: pip install chatterbox-tts")
            self.load_model_btn.config(state="disabled")
        else:
            self.log_message("✅ ChatterboxTTS library found")
        
        if not NLTK_AVAILABLE:
            self.log_message("⚠️ NLTK not available. Using basic sentence splitting.")
        else:
            self.log_message("✅ NLTK library found")
    
    def load_model_thread(self):
        """Load model in a separate thread"""
        if self.is_generating:
            messagebox.showwarning("Warning", "Please wait for current operation to complete.")
            return
        
        thread = threading.Thread(target=self.load_model)
        thread.daemon = True
        thread.start()
    
    def load_model(self):
        """Load the ChatterboxTTS model"""
        if not CHATTERBOX_AVAILABLE:
            messagebox.showerror("Error", "ChatterboxTTS not installed!")
            return
        
        self.load_model_btn.config(state="disabled")
        self.log_message("🔄 Loading ChatterboxTTS model...")
        
        try:
            device_preference = self.device_var.get()
            
            if device_preference == "auto":
                devices_to_try = ["cuda", "cpu"] if torch.cuda.is_available() else ["cpu"]
            else:
                devices_to_try = [device_preference]
            
            for device in devices_to_try:
                try:
                    self.log_message(f"Trying to load model on {device}...")
                    self.model = ChatterboxTTS.from_pretrained(device=device)
                    self.device = device
                    self.log_message(f"✅ Model loaded successfully on {device}!")
                    self.model_status.config(text=f"Model loaded on {device}", foreground="green")
                    
                    if device == "cuda":
                        gpu_name = torch.cuda.get_device_name(0)
                        self.log_message(f"GPU: {gpu_name}")
                        torch.cuda.empty_cache()
                    
                    break
                    
                except Exception as e:
                    self.log_message(f"❌ Failed to load on {device}: {str(e)}")
                    continue
            
            if self.model is None:
                raise RuntimeError("Failed to load model on any device")
                
        except Exception as e:
            self.log_message(f"❌ Error loading model: {str(e)}")
            self.model_status.config(text="Model loading failed", foreground="red")
            messagebox.showerror("Error", f"Failed to load model: {str(e)}")
        
        finally:
            self.load_model_btn.config(state="normal")
    
    def load_voice_sample(self):
        """Load a voice sample for cloning"""
        file_path = filedialog.askopenfilename(
            title="Select Voice Sample",
            filetypes=[
                ("Audio files", "*.wav *.mp3 *.flac *.ogg"),
                ("WAV files", "*.wav"),
                ("MP3 files", "*.mp3"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.voice_clone_path = file_path
            filename = os.path.basename(file_path)
            self.voice_path_var.set(f"Loaded: {filename}")
            self.log_message(f"📁 Voice sample loaded: {filename}")
    
    def clear_voice_sample(self):
        """Clear the loaded voice sample"""
        self.voice_clone_path = None
        self.voice_path_var.set("No voice sample loaded")
        self.log_message("❌ Voice sample cleared")
    
    def browse_save_location(self):
        """Browse for save location"""
        directory = filedialog.askdirectory(
            title="Select Save Location",
            initialdir=self.save_location
        )
        
        if directory:
            self.save_location = directory
            self.save_path_var.set(directory)
            self.log_message(f"📂 Save location set to: {directory}")
    
    def split_into_sentences(self, text):
        """Split text into sentences"""
        if NLTK_AVAILABLE:
            try:
                from nltk.tokenize import sent_tokenize
                sentences = sent_tokenize(text)
            except:
                # Fallback to regex
                sentences = re.split(r'[.!?]+', text)
                sentences = [s.strip() + '.' for s in sentences if s.strip()]
        else:
            # Simple regex-based splitting
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() + '.' for s in sentences if s.strip()]
        
        return [s for s in sentences if s.strip()]
    
    def generate_audio_thread(self):
        """Generate audio in a separate thread"""
        if self.is_generating:
            messagebox.showwarning("Warning", "Generation already in progress!")
            return
        
        if self.model is None:
            messagebox.showerror("Error", "Please load the model first!")
            return
        
        text = self.text_input.get('1.0', tk.END).strip()
        if not text:
            messagebox.showwarning("Warning", "Please enter some text to generate!")
            return
        
        thread = threading.Thread(target=self.generate_audio, args=(text,))
        thread.daemon = True
        thread.start()
    
    def generate_audio(self, text):
        """Generate audio from text"""
        self.is_generating = True
        self.generate_btn.config(state="disabled")
        self.progress_var.set(0)

        try:
            # Get advanced parameters
            exaggeration = self.exaggeration_var.get()
            cfg_weight = self.cfg_weight_var.get()
            temperature = self.temperature_var.get()

            # Handle random seed
            seed_str = self.random_seed_var.get().strip()
            if seed_str and seed_str != "0":
                try:
                    seed = int(seed_str)
                    torch.manual_seed(seed)
                    if torch.cuda.is_available():
                        torch.cuda.manual_seed(seed)
                    self.log_message(f"🎲 Using random seed: {seed}")
                except ValueError:
                    self.log_message("⚠️ Invalid seed value, using random generation")
            else:
                self.log_message("🎲 Using random generation")

            self.log_message(f"🎛️ Parameters - Exaggeration: {exaggeration:.2f}, CFG/Pace: {cfg_weight:.2f}, Temperature: {temperature:.2f}")

            # Clear CUDA cache if using GPU
            if self.device == "cuda":
                torch.cuda.empty_cache()
            
            if self.sentence_mode_var.get():
                # Generate sentence by sentence
                sentences = self.split_into_sentences(text)
                self.log_message(f"🔄 Generating audio for {len(sentences)} sentences...")
                
                self.generated_sentences = []
                self.audio_files = []
                
                for i, sentence in enumerate(sentences):
                    try:
                        self.log_message(f"📝 Sentence {i+1}/{len(sentences)}: {sentence[:50]}...")
                        
                        with torch.no_grad():
                            if self.voice_clone_path:
                                wav = self.model.generate(
                                    sentence,
                                    audio_prompt_path=self.voice_clone_path,
                                    exaggeration=exaggeration,
                                    cfg_weight=cfg_weight,
                                    temperature=temperature
                                )
                            else:
                                wav = self.model.generate(
                                    sentence,
                                    exaggeration=exaggeration,
                                    cfg_weight=cfg_weight,
                                    temperature=temperature
                                )
                        
                        self.generated_sentences.append(wav)
                        
                        # Save individual sentence
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"sentence_{i+1:03d}_{timestamp}.wav"
                        filepath = os.path.join(self.save_location, filename)
                        ta.save(filepath, wav, self.model.sr)
                        self.audio_files.append(filepath)
                        
                        progress = ((i + 1) / len(sentences)) * 100
                        self.progress_var.set(progress)
                        
                        # Clear CUDA cache after each sentence
                        if self.device == "cuda":
                            torch.cuda.empty_cache()
                            
                    except Exception as e:
                        self.log_message(f"❌ Error generating sentence {i+1}: {str(e)}")
                        continue
                
                if self.generated_sentences:
                    self.log_message(f"✅ Generated {len(self.generated_sentences)} sentence audio files!")
                    
                    # Auto-combine if enabled
                    if self.auto_combine_var.get():
                        self.combine_all_audio()
                    
                    # Auto-play if enabled
                    if self.auto_play_var.get() and self.audio_files:
                        self.play_audio_file(self.audio_files[-1])
                
            else:
                # Generate entire text at once
                self.log_message("🔄 Generating audio for entire text...")
                
                with torch.no_grad():
                    if self.voice_clone_path:
                        wav = self.model.generate(
                            text,
                            audio_prompt_path=self.voice_clone_path,
                            exaggeration=exaggeration,
                            cfg_weight=cfg_weight,
                            temperature=temperature
                        )
                    else:
                        wav = self.model.generate(
                            text,
                            exaggeration=exaggeration,
                            cfg_weight=cfg_weight,
                            temperature=temperature
                        )
                
                # Save audio
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"full_text_{timestamp}.wav"
                filepath = os.path.join(self.save_location, filename)
                ta.save(filepath, wav, self.model.sr)
                self.audio_files = [filepath]
                
                self.progress_var.set(100)
                self.log_message("✅ Audio generation complete!")
                
                # Auto-play if enabled
                if self.auto_play_var.get():
                    self.play_audio_file(filepath)
        
        except Exception as e:
            self.log_message(f"❌ Error generating audio: {str(e)}")
            messagebox.showerror("Error", f"Audio generation failed: {str(e)}")
        
        finally:
            self.is_generating = False
            self.generate_btn.config(state="normal")
            self.progress_var.set(0)
    
    def combine_all_audio(self):
        """Combine all generated sentence audio files"""
        if not self.generated_sentences:
            messagebox.showwarning("Warning", "No sentences to combine!")
            return
        
        try:
            self.log_message("🔗 Combining all sentences into one audio file...")
            
            # Combine audio tensors
            combined_audio = torch.cat(self.generated_sentences, dim=-1)
            
            # Save combined audio
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"combined_{timestamp}.wav"
            filepath = os.path.join(self.save_location, filename)
            ta.save(filepath, combined_audio, self.model.sr)
            
            self.log_message(f"✅ Combined audio saved as: {filename}")
            
            # Auto-play if enabled
            if self.auto_play_var.get():
                self.play_audio_file(filepath)
                
        except Exception as e:
            self.log_message(f"❌ Error combining audio: {str(e)}")
            messagebox.showerror("Error", f"Failed to combine audio: {str(e)}")
    
    def play_audio_file(self, filepath):
        """Play an audio file using pygame"""
        try:
            pygame.mixer.music.load(filepath)
            pygame.mixer.music.play()
            filename = os.path.basename(filepath)
            self.log_message(f"▶️ Playing: {filename}")
        except Exception as e:
            self.log_message(f"❌ Error playing audio: {str(e)}")
    
    def play_last_audio(self):
        """Play the last generated audio file"""
        if not self.audio_files:
            messagebox.showinfo("Info", "No audio files to play!")
            return
        
        self.play_audio_file(self.audio_files[-1])
    
    def stop_audio(self):
        """Stop audio playback"""
        pygame.mixer.music.stop()
        self.log_message("⏹️ Audio playback stopped")
    
    def clear_all(self):
        """Clear all generated audio files and reset"""
        try:
            # Stop any playing audio
            pygame.mixer.music.stop()
            
            # Clear in-memory data
            self.generated_sentences = []
            deleted_count = 0
            
            # Delete audio files
            for filepath in self.audio_files:
                try:
                    if os.path.exists(filepath):
                        os.unlink(filepath)
                        deleted_count += 1
                except Exception as e:
                    self.log_message(f"Warning: Could not delete {filepath}: {str(e)}")
            
            self.audio_files = []
            self.progress_var.set(0)
            
            if deleted_count > 0:
                self.log_message(f"🗑️ Cleared {deleted_count} audio files")
            else:
                self.log_message("🗑️ No files to clear")
                
        except Exception as e:
            self.log_message(f"❌ Error clearing files: {str(e)}")
    
    def open_save_folder(self):
        """Open the save folder in file explorer"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.save_location)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{self.save_location}"' if sys.platform == 'darwin' else f'xdg-open "{self.save_location}"')
        except Exception as e:
            self.log_message(f"❌ Error opening folder: {str(e)}")
    
    def load_settings(self):
        """Load settings from file"""
        settings_file = "chatterbox_settings.json"
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r') as f:
                    settings = json.load(f)
                
                self.save_location = settings.get('save_location', self.save_location)
                self.save_path_var.set(self.save_location)
                self.device_var.set(settings.get('device', 'auto'))
                self.sentence_mode_var.set(settings.get('sentence_mode', True))
                self.auto_combine_var.set(settings.get('auto_combine', True))
                self.auto_play_var.set(settings.get('auto_play', False))

                # Load advanced parameters
                self.exaggeration_var.set(settings.get('exaggeration', 0.5))
                self.cfg_weight_var.set(settings.get('cfg_weight', 0.5))
                self.temperature_var.set(settings.get('temperature', 0.8))
                self.random_seed_var.set(settings.get('random_seed', '0'))

                # Update parameter labels
                self.update_exaggeration_label()
                self.update_cfg_weight_label()
                self.update_temperature_label()
                
                self.log_message("✅ Settings loaded")
            except Exception as e:
                self.log_message(f"Warning: Could not load settings: {str(e)}")
    
    def save_settings(self):
        """Save current settings to file"""
        settings = {
            'save_location': self.save_location,
            'device': self.device_var.get(),
            'sentence_mode': self.sentence_mode_var.get(),
            'auto_combine': self.auto_combine_var.get(),
            'auto_play': self.auto_play_var.get(),
            'exaggeration': self.exaggeration_var.get(),
            'cfg_weight': self.cfg_weight_var.get(),
            'temperature': self.temperature_var.get(),
            'random_seed': self.random_seed_var.get()
        }
        
        try:
            with open("chatterbox_settings.json", 'w') as f:
                json.dump(settings, f, indent=2)
        except Exception as e:
            self.log_message(f"Warning: Could not save settings: {str(e)}")
    
    def on_closing(self):
        """Handle application closing"""
        self.save_settings()
        
        # Stop any playing audio
        pygame.mixer.music.stop()
        
        # Ask if user wants to delete generated files
        if self.audio_files:
            if messagebox.askyesno("Clean Up", "Delete all generated audio files before closing?"):
                self.clear_all()
        
        self.root.destroy()

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = ChatterboxTTSApp(root)
    
    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    # Start the GUI
    root.mainloop()

if __name__ == "__main__":
    main()